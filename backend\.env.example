# API Keys
GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
OPENAI_API_KEY="YOUR_OPENAI_API_KEY_HERE"

# Database connections - 使用 MySQL 8+ (推荐)
# 格式: mysql+pymysql://<user>:<password>@<host>:<port>/<dbname>
DATABASE_URL="mysql+pymysql://root:your_mysql_password@localhost:3306/personality_clone_db?charset=utf8mb4"

# 暂时注释掉未使用的数据库配置
# NEO4J_URI="bolt://localhost:7687"
# NEO4J_USER="neo4j"
# NEO4J_PASSWORD="password"
# CHROMA_HOST="localhost"
# CHROMA_PORT="8001"
# REDIS_URL="redis://localhost:6500"
# ELASTICSEARCH_URL="http://localhost:9200"

# Security
SECRET_KEY="your-secret-key-here-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application settings
DEBUG=true
LOG_LEVEL="INFO"
MAX_CONVERSATION_HISTORY=100
PERSONALITY_ANALYSIS_DEPTH=5

# Voice analysis (optional)
AZURE_SPEECH_KEY=""
AZURE_SPEECH_REGION=""

# Text analysis
SENTIMENT_MODEL="cardiffnlp/twitter-roberta-base-sentiment-latest"
EMOTION_MODEL="j-hartmann/emotion-english-distilroberta-base"

# AI Model Configuration
# 使用 1.5 Pro，因为它在理解复杂指令和生成结构化输出方面比 flash 更强大
GEMINI_MODEL_NAME="models/gemini-1.5-pro-latest"
