"""
人格模型缓存服务
实现内存优先的架构，提升AI角色对话性能
"""

import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from app.database.models import PersonalityProfile, Event, Belief, Entity, FamilyMember
import structlog

# RAG相关导入
try:
    from sentence_transformers import SentenceTransformer
    RAG_ENABLED = True
except ImportError:
    RAG_ENABLED = False

logger = structlog.get_logger()

class PersonalityModel:
    """内存中的完整人格模型"""
    
    def __init__(self, profile_data: Dict[str, Any]):
        self.profile_id: str = profile_data['profile_id']
        self.static_data: Dict[str, Any] = profile_data
        self.memory_corpus: List[str] = []
        self.memory_objects: List[Any] = []
        self.corpus_embeddings = None

    def build_memory_corpus(self):
        """构建记忆语料库"""
        self.memory_corpus = []
        self.memory_objects = []
        
        # 添加事件记忆
        for event in self.static_data.get('events', []):
            if event.get('full_narrative'):
                self.memory_corpus.append(f"我经历过：{event['full_narrative']}")
                self.memory_objects.append(event)
        
        # 添加信念记忆
        for belief in self.static_data.get('beliefs', []):
            if belief.get('statement'):
                self.memory_corpus.append(f"我相信：{belief['statement']}")
                self.memory_objects.append(belief)
        
        logger.info("Memory corpus built", 
                   profile_id=self.profile_id, 
                   count=len(self.memory_corpus))

    async def encode_corpus(self, rag_model: SentenceTransformer):
        """异步生成并缓存记忆向量"""
        if not self.memory_corpus:
            return
        
        logger.info("Encoding memory corpus...", profile_id=self.profile_id)
        self.corpus_embeddings = await asyncio.to_thread(
            rag_model.encode, self.memory_corpus, convert_to_tensor=True
        )
        logger.info("Memory corpus encoded successfully.", profile_id=self.profile_id)

class PersonalityCache:
    """人格模型内存缓存 - 单例模式"""
    
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(PersonalityCache, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.cache: Dict[str, PersonalityModel] = {}
            self.rag_model = None
            
            # 初始化RAG模型
            if RAG_ENABLED:
                try:
                    model_name = 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2'
                    self.rag_model = SentenceTransformer(model_name)
                    logger.info("RAG model loaded successfully", model=model_name)
                except Exception as e:
                    logger.error("Failed to load RAG model", error=e)
                    self.rag_model = None
            else:
                logger.warning("sentence-transformers not available, RAG disabled")
            
            self.initialized = True
            logger.info("PersonalityCache initialized")

    async def get_personality(self, personality_id: str, db: AsyncSession) -> Optional[PersonalityModel]:
        """获取人格模型，如果不在缓存中则从数据库加载"""
        if personality_id not in self.cache:
            logger.info("Cache miss. Loading personality from DB", profile_id=personality_id)
            profile = await self._load_from_db(personality_id, db)
            if not profile:
                return None
            
            model = PersonalityModel(profile)
            model.build_memory_corpus()
            
            # 如果RAG模型可用，编码记忆向量
            if self.rag_model:
                await model.encode_corpus(self.rag_model)

            self.cache[personality_id] = model
        
        return self.cache[personality_id]

    async def _load_from_db(self, personality_id: str, db: AsyncSession) -> Optional[Dict[str, Any]]:
        """从数据库加载完整的人格数据"""
        try:
            # 使用selectinload预加载所有相关数据
            result = await db.execute(
                select(PersonalityProfile)
                .where(PersonalityProfile.profile_id == personality_id)
                .options(
                    selectinload(PersonalityProfile.events),
                    selectinload(PersonalityProfile.beliefs),
                    selectinload(PersonalityProfile.entities),
                    selectinload(PersonalityProfile.family_members)
                )
            )
            p = result.scalar_one_or_none()
            if not p:
                return None

            # 转换为字典格式
            return {
                "profile_id": str(p.profile_id),
                "target_name": p.target_name,
                "description": p.description,
                "big_five": {
                    "openness": p.openness_score or 0.5,
                    "conscientiousness": p.conscientiousness_score or 0.5,
                    "extraversion": p.extraversion_score or 0.5,
                    "agreeableness": p.agreeableness_score or 0.5,
                    "neuroticism": p.neuroticism_score or 0.5
                },
                "attachment_style": p.attachment_style or 'secure',
                "cultural_background": p.cultural_background or {},
                "events": [
                    {
                        "event_id": str(event.event_id),
                        "title": event.title,
                        "age_at_event": event.age_at_event,
                        "event_type": event.event_type,
                        "emotional_impact": event.emotional_impact,
                        "full_narrative": event.full_narrative,
                        "salience": event.salience,
                        "last_recalled_at": event.last_recalled_at,
                        "source": event.source
                    }
                    for event in p.events
                ],
                "beliefs": [
                    {
                        "belief_id": str(belief.belief_id),
                        "statement": belief.statement,
                        "belief_category": belief.belief_category,
                        "conviction_strength": belief.conviction_strength,
                        "full_explanation": belief.full_explanation,
                        "salience": belief.salience,
                        "last_recalled_at": belief.last_recalled_at,
                        "source": belief.source
                    }
                    for belief in p.beliefs
                ],
                "entities": [
                    {
                        "entity_id": str(entity.entity_id),
                        "name": entity.name,
                        "entity_type": entity.entity_type,
                        "relationship_type": entity.relationship_type,
                        "emotional_valence": entity.emotional_valence,
                        "importance_score": entity.importance_score
                    }
                    for entity in p.entities
                ],
                "family_members": [
                    {
                        "member_id": str(member.member_id),
                        "relationship_type": member.relationship_type,
                        "name": member.name,
                        "personality_summary": member.personality_summary,
                        "parenting_style": member.parenting_style
                    }
                    for member in p.family_members
                ],
                "communication_style": {
                    "response_length": p.average_response_length or 0.5,
                    "vocabulary_complexity": p.vocabulary_complexity or 0.5,
                    "emotional_expressiveness": p.emotional_expressiveness or 0.5
                }
            }
            
        except Exception as e:
            logger.error("Failed to load personality from database", 
                        personality_id=personality_id, error=str(e))
            return None

    def invalidate_cache(self, personality_id: str):
        """使指定人格的缓存失效"""
        if personality_id in self.cache:
            del self.cache[personality_id]
            logger.info("Cache invalidated", profile_id=personality_id)

    def clear_cache(self):
        """清空所有缓存"""
        self.cache.clear()
        logger.info("All cache cleared")

# 单例实例
personality_cache = PersonalityCache()
