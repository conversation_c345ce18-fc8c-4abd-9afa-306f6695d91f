#!/usr/bin/env python3
"""
优化后的系统启动脚本
专注于AI角色对话功能
"""

import os
import sys
import subprocess
import time
import asyncio
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("🎭" + "="*60 + "🎭")
    print("    100% 人格复刻系统 - 优化版")
    print("    专注于AI角色对话功能")
    print("🎭" + "="*60 + "🎭")

def check_requirements():
    """检查系统要求"""
    print("\n🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 10):
        print("❌ Python版本需要3.10或更高")
        return False
    print("✅ Python版本检查通过")
    
    # 不再检查Docker，现在使用MySQL
    print("✅ 使用MySQL数据库，请确保MySQL服务已启动")
    
    # 检查环境变量
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("⚠️  未找到.env文件，将使用默认配置")
    else:
        print("✅ 环境配置文件存在")
    
    return True

# start_database函数已删除，现在使用SQLite无需启动数据库服务

def install_dependencies():
    """安装Python依赖"""
    print("\n📦 安装Python依赖...")
    
    try:
        # 切换到backend目录
        os.chdir("backend")
        
        # 检查虚拟环境
        venv_path = Path("venv")
        if not venv_path.exists():
            print("🔧 创建虚拟环境...")
            subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
        
        # 激活虚拟环境并安装依赖
        if os.name == 'nt':  # Windows
            pip_path = venv_path / "Scripts" / "pip"
        else:  # Unix/Linux/macOS
            pip_path = venv_path / "bin" / "pip"
        
        print("📥 安装依赖包...")
        subprocess.run([
            str(pip_path), 'install', '-r', 'requirements.txt'
        ], check=True)
        
        print("✅ 依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    finally:
        os.chdir("..")

# 数据库初始化函数已删除，现在只使用 migrate.py

def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    
    try:
        os.chdir("backend")
        
        if os.name == 'nt':  # Windows
            python_path = Path("venv") / "Scripts" / "python"
        else:  # Unix/Linux/macOS
            python_path = Path("venv") / "bin" / "python"
        
        print("🌐 后端服务启动中...")
        print("📍 API地址: http://localhost:8000")
        print("📖 API文档: http://localhost:8000/docs")
        
        # 启动FastAPI服务
        subprocess.run([
            str(python_path), '-m', 'uvicorn', 'main:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ])
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断服务")
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
    finally:
        os.chdir("..")

def start_frontend():
    """启动前端服务（可选）"""
    print("\n🎨 启动前端服务...")
    
    frontend_path = Path("frontend")
    if not frontend_path.exists():
        print("⚠️  前端目录不存在，跳过前端启动")
        return False
    
    try:
        os.chdir("frontend")
        
        # 检查node_modules
        if not Path("node_modules").exists():
            print("📦 安装前端依赖...")
            subprocess.run(['npm', 'install'], check=True)
        
        print("🌐 前端服务启动中...")
        print("📍 前端地址: http://localhost:5173")
        
        # 启动Vite开发服务器
        subprocess.run(['npm', 'run', 'dev'])
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断服务")
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
    finally:
        os.chdir("..")

def show_usage_guide():
    """显示使用指南"""
    print("\n📚 使用指南:")
    print("=" * 50)
    print("1. 🎭 生成角色数据:")
    print("   cd backend && python character_generator.py")
    print()
    print("2. 🔄 批量生成角色:")
    print("   cd backend && python batch_character_generator.py")
    print()
    print("3. 🛠️  管理角色:")
    print("   cd backend && python character_manager.py")
    print()
    print("4. 🌐 API文档:")
    print("   http://localhost:8000/docs")
    print()
    print("5. 💬 AI角色对话:")
    print("   使用 /api/v1/simulation/* 端点")
    print("=" * 50)

def main():
    """主函数"""
    print_banner()
    
    # 检查系统要求
    if not check_requirements():
        print("\n❌ 系统要求检查失败，请解决上述问题后重试")
        return 1
    
    # MySQL数据库需要确保服务已启动
    print("\n✅ 使用MySQL数据库，请确保MySQL服务已启动")
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败")
        return 1
    
    print("\n✅ 系统初始化完成!")
    print("\n" + "="*50)
    print("🚀 首次运行或模型更新后，请务必执行数据库迁移！")
    print("   在 backend 目录下运行: python migrate.py upgrade head")
    print("="*50)
    
    # 显示使用指南
    show_usage_guide()
    
    # 询问是否启动服务
    print("\n🚀 是否启动后端服务? (y/N): ", end="")
    choice = input().strip().lower()
    
    if choice == 'y':
        start_backend()
    else:
        print("\n✅ 系统准备就绪!")
        print("💡 提示: 系统现在使用MySQL数据库，请确保MySQL服务已启动。")
        print("💡 提示: 运行 'python start_optimized.py' 并选择 'y' 来启动后端服务")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  启动过程被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 启动过程中出现错误: {e}")
        sys.exit(1)
