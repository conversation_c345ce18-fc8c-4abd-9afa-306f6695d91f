<template>
  <div class="chat-interface">
    <!-- 人格选择器 -->
    <el-card v-if="!selectedPersonality" class="personality-selector">
      <template #header>
        <h3>选择要对话的人格档案</h3>
      </template>

      <div v-if="personalities.length === 0" class="empty-state">
        <el-icon size="48"><User /></el-icon>
        <p>暂无人格档案</p>
        <el-button type="primary" @click="createPersonality">
          创建人格档案
        </el-button>
      </div>

      <div v-else class="personality-list">
        <div
          v-for="personality in personalities"
          :key="personality.personality_id"
          class="personality-item"
          @click="selectPersonality(personality)"
        >
          <div class="personality-info">
            <h4>{{ personality.target_name }}</h4>
            <p>{{ personality.description || '暂无描述' }}</p>
            <el-progress
              :percentage="Math.round(personality.completion_percentage || 0)"
              :stroke-width="4"
              :show-text="false"
            />
          </div>
          <el-button type="primary">开始对话</el-button>
        </div>
      </div>
    </el-card>

    <!-- 对话界面 -->
    <div v-else class="chat-container">
      <!-- 对话头部 -->
      <el-card class="chat-header">
        <div class="header-content">
          <div class="personality-info">
            <h3>{{ selectedPersonality.target_name }}</h3>
            <p>完成度: {{ Math.round(selectedPersonality.completion_percentage || 0) }}%</p>
          </div>
          <div class="actions">
            <el-button @click="showHistoryDialog = true" type="info" plain>
              <el-icon><Clock /></el-icon>
              历史对话
            </el-button>
            <el-button @click="selectedPersonality = null">
              <el-icon><ArrowLeft /></el-icon>
              返回选择
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 消息列表 -->
      <el-card class="chat-messages" v-loading="loading">
        <div class="messages-container" ref="messagesContainer">
          <div
            v-for="message in messages"
            :key="message.id"
            :class="['message', message.sender]"
          >
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <div v-if="messages.length === 0" class="empty-messages">
            <el-icon size="48"><ChatDotRound /></el-icon>
            <p>开始与 {{ selectedPersonality.target_name }} 对话吧！</p>
          </div>
        </div>
      </el-card>

      <!-- 输入区域 -->
      <el-card class="chat-input">
        <div class="input-container">
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="3"
            placeholder="请输入您的消息..."
            @keydown.ctrl.enter="sendMessage"
          />
          <div class="input-actions">
            <div class="left-actions">
              <el-button
                size="small"
                @click="passTime"
                :loading="passingTime"
                title="模拟AI度过4个小时，并产生新的想法和记忆"
                :disabled="!conversationId"
              >
                <el-icon><Timer /></el-icon>
                过了4小时
              </el-button>
            </div>
            <div class="right-actions">
              <span class="tip">Ctrl + Enter 发送</span>
              <el-button
                type="primary"
                :loading="sending"
                :disabled="!userInput.trim()"
                @click="sendMessage"
              >
                发送
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 历史对话对话框 -->
    <el-dialog
      v-model="showHistoryDialog"
      title="历史对话"
      width="600px"
      :before-close="handleHistoryDialogClose"
    >
      <div v-loading="historyLoading">
        <div v-if="historyConversations.length === 0" class="no-history">
          <el-empty description="暂无历史对话记录">
            <el-button type="primary" @click="startNewConversation">
              开始新对话
            </el-button>
          </el-empty>
        </div>
        <div v-else>
          <div class="history-actions">
            <el-button type="primary" @click="startNewConversation">
              <el-icon><Plus /></el-icon>
              开始新对话
            </el-button>
          </div>
          <el-list>
            <el-list-item
              v-for="conversation in historyConversations"
              :key="conversation.conversation_id"
              class="history-item"
            >
              <div class="conversation-info">
                <div class="conversation-time">
                  {{ formatHistoryTime(conversation.started_at) }}
                </div>
                <div class="conversation-status">
                  {{ conversation.ended_at ? '已结束' : '进行中' }}
                </div>
              </div>
              <div class="conversation-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="continueConversation(conversation.conversation_id)"
                >
                  {{ conversation.ended_at ? '查看' : '继续' }}
                </el-button>
              </div>
            </el-list-item>
          </el-list>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ChatDotRound, User, ArrowLeft, Clock, Plus, Timer } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const route = useRoute()

// 响应式数据
const personalities = ref([])
const selectedPersonality = ref(null)
const messages = ref([])
const userInput = ref('')
const loading = ref(false)
const sending = ref(false)
const conversationId = ref(null)
const messagesContainer = ref(null)
const showHistoryDialog = ref(false)
const historyLoading = ref(false)
const historyConversations = ref([])
const passingTime = ref(false)

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const selectPersonality = async (personality) => {
  selectedPersonality.value = personality
  // 先检查是否有历史对话
  await loadHistoryConversations()

  if (historyConversations.value.length > 0) {
    // 如果有历史对话，显示对话框让用户选择
    showHistoryDialog.value = true
  } else {
    // 如果没有历史对话，直接开始新对话
    await startNewConversation()
  }
}

const startNewConversation = async () => {
  try {
    loading.value = true
    showHistoryDialog.value = false

    // 使用新的模拟API启动对话
    const response = await apiMethods.simulation.start(
      selectedPersonality.value.personality_id,
      '你好，很高兴认识你！'
    )

    conversationId.value = response.data.conversation_id

    // 添加用户的初始消息和AI的回复
    messages.value = [
      {
        id: Date.now(),
        sender: 'user',
        content: '你好，很高兴认识你！',
        timestamp: new Date()
      },
      {
        id: Date.now() + 1,
        sender: 'ai',
        content: response.data.ai_response,
        timestamp: new Date()
      }
    ]

    await scrollToBottom()
  } catch (error) {
    console.error('启动对话失败:', error)
    ElMessage.error('启动对话失败，请重试')
  } finally {
    loading.value = false
  }
}

const sendMessage = async () => {
  if (!userInput.value.trim() || sending.value) return

  const messageText = userInput.value.trim()
  userInput.value = ''

  // 添加用户消息
  messages.value.push({
    id: Date.now(),
    sender: 'user',
    content: messageText,
    timestamp: new Date()
  })

  await scrollToBottom()

  try {
    sending.value = true

    // 使用新的模拟API发送消息
    const response = await apiMethods.simulation.chat(
      conversationId.value,
      messageText
    )

    // 添加AI回复
    messages.value.push({
      id: Date.now() + 1,
      sender: 'ai',
      content: response.data.ai_response,
      timestamp: new Date()
    })

    await scrollToBottom()
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    sending.value = false
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 历史对话相关方法
const loadHistoryConversations = async () => {
  if (!selectedPersonality.value) return

  try {
    historyLoading.value = true
    const response = await apiMethods.simulation.getConversations(
      selectedPersonality.value.personality_id
    )
    historyConversations.value = response.data || []
  } catch (error) {
    console.error('加载历史对话失败:', error)
    ElMessage.error('加载历史对话失败')
  } finally {
    historyLoading.value = false
  }
}

const continueConversation = async (conversationIdToLoad) => {
  try {
    loading.value = true
    showHistoryDialog.value = false

    // 设置当前对话ID
    conversationId.value = conversationIdToLoad

    // 加载历史消息
    const response = await apiMethods.simulation.getMessages(conversationIdToLoad)
    const historyMessages = response.data || []

    // 转换消息格式
    messages.value = historyMessages.map((msg, index) => ({
      id: index,
      sender: msg.sender,
      content: msg.content,
      timestamp: new Date(msg.timestamp)
    }))

    await scrollToBottom()
    ElMessage.success('历史对话加载成功')
  } catch (error) {
    console.error('加载历史对话失败:', error)
    ElMessage.error('加载历史对话失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleHistoryDialogClose = () => {
  showHistoryDialog.value = false
}

const formatHistoryTime = (timeStr) => {
  if (!timeStr) return '未知时间'
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 时间流逝功能
const passTime = async () => {
  if (!conversationId.value) {
    ElMessage.warning('请先开始对话')
    return
  }

  passingTime.value = true
  try {
    const response = await apiMethods.simulation.passTime({
      conversation_id: conversationId.value,
      hours_passed: 4
    })

    // 在聊天窗口显示一条系统消息，告知用户AI产生了新记忆
    messages.value.push({
      id: Date.now(),
      sender: 'system', // 使用一个新的sender类型
      content: `[系统提示：AI度过了4个小时，并产生了新的记忆："${response.data.new_memory_generated}"]`,
      timestamp: new Date()
    })

    await scrollToBottom()
    ElMessage.success('时间流逝模拟成功，AI已产生新记忆。')
  } catch (error) {
    console.error('模拟时间流逝失败:', error)
    ElMessage.error('模拟时间流逝失败')
  } finally {
    passingTime.value = false
  }
}

const loadPersonalities = async () => {
  try {
    const response = await apiMethods.personalities.list()
    personalities.value = response.data || []

    // 如果URL中有personalityId参数，自动选择
    const personalityId = route.params.personalityId
    if (personalityId) {
      const personality = personalities.value.find(p => p.personality_id === personalityId)
      if (personality) {
        await selectPersonality(personality)
      }
    }
  } catch (error) {
    console.error('加载人格档案失败:', error)
    ElMessage.error('加载失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  loadPersonalities()
})
</script>

<style scoped>
.chat-interface {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.personality-selector {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.personality-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.personality-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.personality-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.personality-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.personality-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-header {
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-messages {
  flex: 1;
  overflow: hidden;
}

.messages-container {
  height: 400px;
  overflow-y: auto;
  padding: 10px 0;
}

.message {
  margin-bottom: 20px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.ai {
  justify-content: flex-start;
}

.message.system {
  justify-content: center;
  margin: 15px 0;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
}

.message.user .message-content {
  background: #409eff;
  color: white;
}

.message.ai .message-content {
  background: #f5f7fa;
  color: #303133;
}

.message.system .message-content {
  background: #e9e9eb;
  color: #909399;
  font-style: italic;
  font-size: 12px;
  max-width: 90%;
  text-align: center;
}

.message-text {
  line-height: 1.5;
  word-wrap: break-word;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 5px;
}

.empty-messages {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.chat-input {
  flex-shrink: 0;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.right-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.tip {
  font-size: 12px;
  color: #909399;
}

/* 历史对话样式 */
.no-history {
  text-align: center;
  padding: 40px 20px;
}

.history-actions {
  margin-bottom: 20px;
  text-align: center;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.history-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.conversation-info {
  flex: 1;
}

.conversation-time {
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.conversation-status {
  font-size: 12px;
  color: #909399;
}

.conversation-actions {
  margin-left: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-interface {
    padding: 10px;
  }

  .message-content {
    max-width: 85%;
  }

  .header-content {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
