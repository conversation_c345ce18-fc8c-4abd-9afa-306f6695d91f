#!/usr/bin/env python3
"""
数字生命系统数据库初始化脚本
用于创建升级后的数据库结构
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.db_session import init_database
from app.database.models import Base
from sqlalchemy.ext.asyncio import create_async_engine
from dotenv import load_dotenv
import structlog

logger = structlog.get_logger()

async def create_fresh_database():
    """创建全新的数据库"""
    load_dotenv()
    
    database_url = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./personality_clone.db")
    
    print("🗃️  数字生命系统 - 数据库初始化")
    print("=" * 50)
    print(f"数据库URL: {database_url}")
    
    # 如果是SQLite，删除旧的数据库文件
    if "sqlite" in database_url:
        db_file = database_url.split("///")[-1]
        if os.path.exists(db_file):
            print(f"🗑️  删除旧数据库文件: {db_file}")
            os.remove(db_file)
    
    try:
        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=True)
        
        # 创建所有表
        async with engine.begin() as conn:
            print("🏗️  创建数据库表...")
            await conn.run_sync(Base.metadata.create_all)
        
        await engine.dispose()
        
        print("✅ 数据库初始化完成!")
        print("\n📋 新增功能:")
        print("- 记忆显化度 (salience)")
        print("- 遗忘曲线 (last_recalled_at)")
        print("- 记忆来源 (source: user_provided, virtual, generated)")
        print("- 关系模型 (affinity, trust)")
        print("- 虚拟时间流逝")
        
        print("\n🚀 下一步:")
        print("1. 运行: python batch_character_generator.py")
        print("2. 启动后端: python main.py")
        print("3. 启动前端: npm run dev")
        
        return True
        
    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        print(f"❌ 数据库初始化失败: {e}")
        return False

async def main():
    """主函数"""
    try:
        success = await create_fresh_database()
        return 0 if success else 1
    except Exception as e:
        logger.error("Initialization failed", error=str(e))
        print(f"❌ 初始化失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
